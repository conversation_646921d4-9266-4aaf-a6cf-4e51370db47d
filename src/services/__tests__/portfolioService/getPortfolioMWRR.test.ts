import { faker } from "@faker-js/faker";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { clearDb, closeDb, connectDb } from "../../../tests/utils/db";
import {
  buildAssetTransaction,
  buildChargeTransaction,
  buildHoldingDTO,
  buildIntraDayPortfolioTicker,
  buildOrder,
  buildPortfolio,
  buildSubscription,
  buildUser,
  buildReward
} from "../../../tests/utils/generateModels";
import { TenorEnum } from "../../../configs/durationConfig";
import PortfolioService from "../../portfolioService";
import { Portfolio, PortfolioDocument, PortfolioModeEnum } from "../../../models/Portfolio";
import { DividendTransactionDocument } from "../../../models/Transaction";
import { RewardDocument } from "../../../models/Reward";
import { UserDocument } from "../../../models/User";
import DateUtil from "../../../utils/dateUtil";
import { ProviderEnum } from "../../../configs/providersConfig";
import { InternalServerError } from "../../../models/ApiErrors";

const { ASSET_CONFIG } = investmentUniverseConfig;

describe("PortfolioService.getPortfolioMWRR", () => {
  beforeAll(async () => {
    await connectDb("getPortfolioMWRR");
  });
  afterAll(async () => {
    await closeDb();
  });
  afterEach(async () => await clearDb());

  describe("when the portfolio is real", () => {
    let portfolio: PortfolioDocument;

    describe("and rewards do not exist", () => {
      describe("and tenor is passed", () => {
        const transactions: {
          dividendTransactions: DividendTransactionDocument[];
          rewards: RewardDocument[];
        } = {
          dividendTransactions: [],
          rewards: []
        };

        beforeEach(async () => {
          const user = await buildUser();
          portfolio = await buildPortfolio({
            owner: user.id,
            mode: PortfolioModeEnum.REAL,
            holdings: [await buildHoldingDTO(true, "equities_eu", 1, { price: 206.69 })]
          });
          await buildIntraDayPortfolioTicker({
            portfolio: portfolio.id,
            pricePerCurrency: { GBP: 206.69 },
            timestamp: new Date(2021, 10, 25)
          });
          const ordersConfig = [
            {
              side: "Buy",
              amount: 50,
              updatedAt: new Date(2021, 7, 18)
            },
            {
              side: "Buy",
              amount: 50,
              updatedAt: new Date(2021, 8, 10)
            },
            {
              side: "Buy",
              amount: 50,
              updatedAt: new Date(2021, 8, 29)
            },
            {
              side: "Buy",
              amount: 50.14,
              updatedAt: new Date(2021, 10, 2)
            }
          ];

          const subscription = await buildSubscription({
            category: "FeeBasedSubscription",
            active: true,
            price: "free_monthly",
            owner: user.id
          });

          // Pending charge transaction with unmatched orders (should not affect calculation)
          const chargeTransactionWithUnmatchedOrders = await buildChargeTransaction({
            owner: user.id,
            portfolio: portfolio.id,
            subscription: subscription.id,
            chargeMethod: "combined",
            status: "Pending",
            createdAt: new Date(2021, 11, 2)
          });
          await buildOrder({
            status: "Pending",
            providers: {
              wealthkernel: {
                status: "Pending",
                id: faker.string.uuid(),
                submittedAt: new Date(2021, 11, 2)
              }
            },
            isin: ASSET_CONFIG["equities_eu"].isin,
            transaction: chargeTransactionWithUnmatchedOrders.id,
            side: "Sell",
            consideration: {
              currency: "GBP",
              amount: 100
            }
          });
          await chargeTransactionWithUnmatchedOrders.populate("orders");

          const transaction = await buildAssetTransaction({
            owner: user.id,
            status: "Settled"
          });
          const orders = await Promise.all(
            ordersConfig.map(({ side, amount, updatedAt }) =>
              buildOrder({
                status: "Matched",
                providers: {
                  wealthkernel: {
                    status: "Matched",
                    id: faker.string.uuid(),
                    submittedAt: updatedAt
                  }
                },
                transaction: transaction.id,
                side: side as "Buy" | "Sell",
                consideration: {
                  originalAmount: amount * 100,
                  amount: amount * 100 - 1,
                  currency: "GBP"
                },
                fees: {
                  executionSpread: {
                    amount: 1,
                    currency: "GBP"
                  },
                  fx: {
                    amount: 0,
                    currency: "GBP"
                  }
                },
                updatedAt,
                filledAt: updatedAt
              })
            )
          );
          transaction.orders = orders.map((order) => order.id);
          await transaction.save();
          await transaction.populate("orders");
        });

        it("should calculate successfully portfolio return value", async () => {
          portfolio = (await Portfolio.findById(portfolio.id).populate([
            { path: "currentTicker" }
          ])) as PortfolioDocument;
          const portfolioReturn = await PortfolioService.getPortfolioMWRR(
            portfolio,
            transactions,
            TenorEnum.ALL_TIME
          );
          expect(portfolioReturn).toEqual({ max: 0.03272709103627453 });
        });
      });

      describe("and tenor is not passed", () => {
        const transactions: {
          dividendTransactions: DividendTransactionDocument[];
          rewards: RewardDocument[];
        } = {
          dividendTransactions: [],
          rewards: []
        };

        beforeEach(async () => {
          const user = await buildUser();
          portfolio = await buildPortfolio({
            owner: user.id,
            mode: PortfolioModeEnum.REAL,
            holdings: [await buildHoldingDTO(true, "equities_eu", 1, { price: 206.69 })]
          });
          await buildIntraDayPortfolioTicker({
            portfolio: portfolio.id,
            pricePerCurrency: { GBP: 206.69 },
            timestamp: new Date(2021, 10, 25)
          });
          const ordersConfig = [
            {
              side: "Buy",
              amount: 50,
              updatedAt: new Date(2021, 7, 18)
            },
            {
              side: "Buy",
              amount: 50,
              updatedAt: new Date(2021, 8, 10)
            },
            {
              side: "Buy",
              amount: 50,
              updatedAt: new Date(2021, 8, 29)
            },
            {
              side: "Buy",
              amount: 50.14,
              updatedAt: new Date(2021, 10, 2)
            }
          ];

          const subscription = await buildSubscription({
            category: "FeeBasedSubscription",
            active: true,
            price: "free_monthly",
            owner: user.id
          });

          // Pending charge transaction with unmatched orders (should not affect calculation)
          const chargeTransactionWithUnmatchedOrders = await buildChargeTransaction({
            owner: user.id,
            portfolio: portfolio.id,
            subscription: subscription.id,
            chargeMethod: "combined",
            status: "Pending",
            createdAt: new Date()
          });
          await buildOrder({
            isin: ASSET_CONFIG["equities_eu"].isin,
            transaction: chargeTransactionWithUnmatchedOrders.id,
            side: "Sell",
            consideration: {
              currency: "GBP",
              amount: 100
            },
            status: "Pending",
            providers: {
              wealthkernel: {
                id: "open-charge-buy-order-id",
                status: "Open",
                submittedAt: new Date()
              }
            }
          });
          await chargeTransactionWithUnmatchedOrders.populate("orders");

          const transaction = await buildAssetTransaction({
            owner: user.id,
            status: "Settled"
          });
          const orders = await Promise.all(
            ordersConfig.map(({ side, amount, updatedAt }) =>
              buildOrder({
                status: "Matched",
                providers: {
                  wealthkernel: {
                    status: "Matched",
                    id: faker.string.uuid(),
                    submittedAt: updatedAt
                  }
                },
                filledAt: updatedAt,
                transaction: transaction.id,
                side: side as "Buy" | "Sell",
                consideration: {
                  amount: amount * 100 - 1,
                  originalAmount: amount * 100,
                  currency: "GBP"
                },
                fees: {
                  executionSpread: {
                    amount: 1,
                    currency: "GBP"
                  },
                  fx: {
                    amount: 0,
                    currency: "GBP"
                  }
                },
                updatedAt
              })
            )
          );
          transaction.orders = orders.map((order) => order.id);
          await transaction.save();
          await transaction.populate("orders");
        });

        it("should calculate successfully portfolio return value", async () => {
          portfolio = (await Portfolio.findById(portfolio.id).populate([
            { path: "currentTicker" }
          ])) as PortfolioDocument;
          const portfolioReturn = await PortfolioService.getPortfolioMWRR(portfolio, transactions);
          expect(portfolioReturn).toMatchObject({
            today: 0,
            "1d": 0,
            "1w": 0,
            "1m": 0,
            "3m": 0,
            "6m": 0,
            "1y": 0,
            max: 0.03272709103627453
          });
        });
      });
    });

    describe("and rewards also exist", () => {
      const transactions: {
        dividendTransactions: DividendTransactionDocument[];
        rewards: RewardDocument[];
      } = {
        dividendTransactions: [],
        rewards: []
      };

      beforeEach(async () => {
        const user = await buildUser();
        portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          holdings: [await buildHoldingDTO(true, "equities_eu", 1, { price: 206.69 / 100 })]
        });
        await buildIntraDayPortfolioTicker({
          portfolio: portfolio.id,
          pricePerCurrency: { GBP: 206.69 / 100 },
          timestamp: new Date(2021, 10, 25)
        });
        const ordersConfig = [
          {
            side: "Buy",
            amount: 50,
            updatedAt: new Date(2021, 7, 18)
          },
          {
            side: "Buy",
            amount: 50,
            updatedAt: new Date(2021, 8, 10)
          },
          {
            side: "Buy",
            amount: 50,
            updatedAt: new Date(2021, 8, 29)
          },
          {
            side: "Buy",
            amount: 50.14,
            updatedAt: new Date(2021, 10, 2)
          }
        ];

        const reward = await buildReward({
          targetUser: user.id,
          consideration: { currency: "GBP", amount: 14, orderAmount: 14, bonusAmount: 14 },
          status: "Settled",
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          },
          order: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Matched"
              }
            }
          },
          updatedAt: new Date(2021, 9, 10)
        });

        const transaction = await buildAssetTransaction({
          owner: user.id,
          status: "Settled"
        });
        const orders = await Promise.all(
          ordersConfig.map(({ side, amount, updatedAt }) =>
            buildOrder({
              status: "Matched",
              providers: {
                wealthkernel: {
                  status: "Matched",
                  id: faker.string.uuid(),
                  submittedAt: updatedAt
                }
              },
              filledAt: updatedAt,
              transaction: transaction.id,
              side: side as "Buy" | "Sell",
              consideration: {
                amount: amount - 1,
                originalAmount: amount,
                currency: "GBP"
              },
              fees: {
                executionSpread: {
                  amount: 1,
                  currency: "GBP"
                },
                fx: {
                  amount: 0,
                  currency: "GBP"
                }
              },
              updatedAt
            })
          )
        );
        transaction.orders = orders.map((order) => order.id);
        await transaction.save();
        await transaction.populate("orders");

        transactions.rewards = [reward];
      });

      it("should calculate successfully portfolio return value", async () => {
        portfolio = (await Portfolio.findById(portfolio.id).populate([
          { path: "currentTicker" }
        ])) as PortfolioDocument;
        const portfolioReturn = await PortfolioService.getPortfolioMWRR(
          portfolio,
          transactions,
          TenorEnum.ALL_TIME
        );
        expect(portfolioReturn).toEqual({ max: -0.03801251517698701 });
      });
    });
  });

  describe("when the portfolio is not real", () => {
    let portfolio: PortfolioDocument;
    const transactions: {
      dividendTransactions: DividendTransactionDocument[];
      rewards: RewardDocument[];
    } = {
      dividendTransactions: [],
      rewards: []
    };

    beforeEach(async () => {
      const user = await buildUser();
      portfolio = await buildPortfolio({ owner: user.id, mode: "VIRTUAL" as any });
    });

    it("should throw error", async () => {
      await expect(async () => await PortfolioService.getPortfolioMWRR(portfolio, transactions)).rejects.toThrow(
        new InternalServerError("Portfolio returns can only be calculated for real portfolios")
      );
    });
  });

  describe("when requested with a TODAY tenor but the portfolio has no ticker for the day", () => {
    const START_INTRADAY_PORTFOLIO_VALUE = 100;

    const transactions: {
      dividendTransactions: DividendTransactionDocument[];
      rewards: RewardDocument[];
    } = {
      dividendTransactions: [],
      rewards: []
    };
    let user: UserDocument;
    let portfolio: PortfolioDocument;

    beforeEach(async () => {
      const TODAY = "2024-03-31";
      Date.now = jest.fn(() => +new Date(TODAY));

      user = await buildUser({ currency: "GBP", portfolioConversionStatus: "completed" });
      portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        cash: {
          GBP: { available: 0, reserved: 0, settled: 0 }
        }
      });
      await buildIntraDayPortfolioTicker({
        timestamp: DateUtil.getDateOfDaysAgo(new Date(TODAY), 1),
        portfolio: portfolio.id,
        pricePerCurrency: {
          USD: START_INTRADAY_PORTFOLIO_VALUE + 1,
          EUR: START_INTRADAY_PORTFOLIO_VALUE - 1,
          GBP: START_INTRADAY_PORTFOLIO_VALUE
        }
      });
    });

    it("should return 0 returns for today", async () => {
      portfolio = await PortfolioService.getPortfolio(portfolio.id, true); // we need the currectTicker to be populated
      const mwrr = await PortfolioService.getPortfolioMWRR(portfolio, transactions, TenorEnum.TODAY);
      expect(mwrr).toEqual({ today: 0 });
    });
  });

  describe("when requested with a TODAY tenor and the portfolio has multiple tickers for the day", () => {
    const START_INTRADAY_PORTFOLIO_VALUE = 100;
    const END_INTRADAY_PORTFOLIO_VALUE = 150;

    const transactions: {
      dividendTransactions: DividendTransactionDocument[];
      rewards: RewardDocument[];
    } = {
      dividendTransactions: [],
      rewards: []
    };
    let user: UserDocument;
    let portfolio: PortfolioDocument;

    beforeEach(async () => {
      const TODAY = new Date("2024-03-31T04:20:00Z");
      const LATER_TODAY = new Date("2024-03-31T12:20:00Z");
      Date.now = jest.fn(() => DateUtil.getStartOfDay(TODAY).getTime());

      user = await buildUser({ currency: "GBP", portfolioConversionStatus: "completed" });
      portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        holdings: [await buildHoldingDTO(true, "equities_uk", 1, { price: END_INTRADAY_PORTFOLIO_VALUE })],
        cash: {
          GBP: { available: 0, reserved: 0, settled: 0 }
        }
      });

      await Promise.all([
        buildIntraDayPortfolioTicker({
          timestamp: DateUtil.getDateOfDaysAgo(new Date(TODAY), 1),
          portfolio: portfolio.id,
          pricePerCurrency: {
            USD: START_INTRADAY_PORTFOLIO_VALUE + 1,
            EUR: START_INTRADAY_PORTFOLIO_VALUE - 1,
            GBP: START_INTRADAY_PORTFOLIO_VALUE
          }
        }),
        buildIntraDayPortfolioTicker({
          timestamp: new Date(TODAY),
          portfolio: portfolio.id,
          pricePerCurrency: {
            USD: START_INTRADAY_PORTFOLIO_VALUE + 1,
            EUR: START_INTRADAY_PORTFOLIO_VALUE - 1,
            GBP: START_INTRADAY_PORTFOLIO_VALUE
          }
        }),
        buildIntraDayPortfolioTicker({
          timestamp: new Date(LATER_TODAY),
          portfolio: portfolio.id,
          pricePerCurrency: {
            USD: END_INTRADAY_PORTFOLIO_VALUE + 1,
            EUR: END_INTRADAY_PORTFOLIO_VALUE - 1,
            GBP: END_INTRADAY_PORTFOLIO_VALUE
          }
        })
      ]);

      const ordersConfig = [
        {
          side: "Buy",
          amount: 400,
          updatedAt: DateUtil.getDateOfDaysAgo(TODAY, 1)
        }
      ];
      const transaction = await buildAssetTransaction({
        owner: user.id,
        status: "Settled"
      });
      const orders = await Promise.all(
        ordersConfig.map(({ side, amount, updatedAt }) =>
          buildOrder({
            status: "Matched",
            providers: {
              wealthkernel: {
                status: "Matched",
                id: faker.string.uuid(),
                submittedAt: updatedAt
              }
            },
            filledAt: updatedAt,
            transaction: transaction.id,
            side: side as "Buy" | "Sell",
            consideration: {
              amount: amount - 1,
              originalAmount: amount,
              currency: "GBP"
            },
            fees: {
              executionSpread: {
                amount: 1,
                currency: "GBP"
              },
              fx: {
                amount: 0,
                currency: "GBP"
              }
            },
            updatedAt
          })
        )
      );
      transaction.orders = orders.map((order) => order.id);
      await transaction.save();
      await transaction.populate("orders");
    });

    it("should return the correct MWRR for today", async () => {
      portfolio = await PortfolioService.getPortfolio(portfolio.id, true); // we need the currectTicker to be populated
      const mwrr = await PortfolioService.getPortfolioMWRR(portfolio, transactions, TenorEnum.TODAY);
      expect(mwrr).toEqual({
        today: expect.closeTo(0.5)
      });
    });
  });

  describe("when the currentTicker is not populated", () => {
    let portfolio: PortfolioDocument;
    const transactions: {
      dividendTransactions: DividendTransactionDocument[];
      rewards: RewardDocument[];
    } = {
      dividendTransactions: [],
      rewards: []
    };

    beforeEach(async () => {
      const user = await buildUser();
      portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        holdings: [await buildHoldingDTO(true, "equities_eu", 1, { price: 206.69 })]
      });
      await buildIntraDayPortfolioTicker({
        portfolio: portfolio.id,
        pricePerCurrency: { GBP: 206.69 },
        timestamp: new Date(2021, 10, 25)
      });

      const transaction = await buildAssetTransaction({
        owner: user.id,
        status: "Settled"
      });
      const order = await buildOrder({
        status: "Matched",
        providers: {
          wealthkernel: {
            status: "Matched",
            id: faker.string.uuid(),
            submittedAt: new Date(2021, 7, 18)
          }
        },
        transaction: transaction.id,
        side: "Buy",
        consideration: {
          originalAmount: 5000,
          amount: 4999,
          currency: "GBP"
        },
        fees: {
          executionSpread: {
            amount: 1,
            currency: "GBP"
          },
          fx: {
            amount: 0,
            currency: "GBP"
          }
        },
        filledAt: new Date(2021, 7, 18)
      });
      transaction.orders = [order.id];
      await transaction.save();
    });

    it("should calculate portfolio MWRR returns", async () => {
      portfolio = (await Portfolio.findById(portfolio.id)) as PortfolioDocument;

      const portfolioReturn = await PortfolioService.getPortfolioMWRR(portfolio, transactions, TenorEnum.ALL_TIME);
      expect(portfolioReturn).toEqual({ max: 3.1338 });
    });
  });
});
